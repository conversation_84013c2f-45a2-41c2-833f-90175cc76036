import React from 'react';
import '../styles/components/Header.css';
import logoEsvc from '../assets/logo-esvc.png';

const Header: React.FC = () => {
  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo">
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Navigation */}
          <nav className="nav">
            <a href="#home" className="nav-link active">Home</a>
            <a href="#stake" className="nav-link">Stake ESVC</a>
            <a href="#funding" className="nav-link">Get Funding</a>
            <a href="#challenge" className="nav-link">Trade Challenge</a>
            <a href="#contact" className="nav-link">Contact Us</a>
          </nav>

          {/* Action Buttons */}
          <div className="header-actions">
            <button className="btn-secondary">Login</button>
            <button className="btn-primary">Get Started</button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
