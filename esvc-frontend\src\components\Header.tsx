import React, { useState } from 'react';
import '../styles/components/Header.css';
import logoEsvc from '../assets/logo-esvc.png';

const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo">
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="nav desktop-nav">
            <a href="#home" className="nav-link active">Home</a>
            <a href="#stake" className="nav-link">Stake ESVC</a>
            <a href="#funding" className="nav-link">Get Funding</a>
            <a href="#challenge" className="nav-link">Trade Challenge</a>
            <a href="#contact" className="nav-link">Contact Us</a>
          </nav>

          {/* Desktop Action Buttons */}
          <div className="header-actions desktop-actions">
            <button className="btn-secondary">Login</button>
            <button className="btn-primary">Get Started</button>
          </div>

          {/* Mobile Hamburger Menu */}
          <button
            className="mobile-menu-toggle"
            onClick={toggleMobileMenu}
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
            aria-expanded={isMobileMenuOpen}
          >
            {/* Hamburger icon */}
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className={`mobile-menu-modal`}>
            <button
              className="mobile-menu-close"
              onClick={closeMobileMenu}
              aria-label="Close menu"
            >
              &times;
            </button>
            <nav className="mobile-nav-modal">
              <a href="#home" className="mobile-nav-link-modal active">Home</a>
              <a href="#stake" className="mobile-nav-link-modal">Stake ESVC</a>
              <a href="#funding" className="mobile-nav-link-modal">Get Funding</a>
              <a href="#challenge" className="mobile-nav-link-modal">Trade Challenge</a>
              <a href="#contact" className="mobile-nav-link-modal">Contact Us</a>
            </nav>
            <div className="mobile-actions-modal">
              <button className="btn-primary mobile-btn-modal">Get Started</button>
              <button className="btn-secondary mobile-btn-secondary-modal">Login</button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
