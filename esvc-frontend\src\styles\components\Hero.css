.hero {
  background: var(--bg-primary);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 80px 0;
}

.hero-content {
  position: relative;
  text-align: center;
  z-index: 2;
}

/* Floating Coins */
.floating-coins {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.coin {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  font-weight: bold;
  color: white;
  animation: float 6s ease-in-out infinite;
}

.coin-1 {
  background: linear-gradient(135deg, #8B0000, #DC143C);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.coin-2 {
  background: linear-gradient(135deg, var(--accent-gold), #FFD700);
  top: 60%;
  left: 15%;
  animation-delay: 2s;
  width: 80px;
  height: 80px;
  font-size: 32px;
}

.coin-3 {
  background: linear-gradient(135deg, #8B0000, #DC143C);
  top: 40%;
  right: 15%;
  animation-delay: 4s;
  width: 60px;
  height: 60px;
  font-size: 24px;
}

.coin-4 {
  background: linear-gradient(135deg, var(--accent-gold), #FFD700);
  top: 15%;
  right: 8%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
  }
}

/* Additional floating animation for variety */
.coin-2 {
  animation: floatReverse 8s ease-in-out infinite;
}

.coin-4 {
  animation: floatSlow 10s ease-in-out infinite;
}

@keyframes floatReverse {
  0%, 100% {
    transform: translateY(0px) rotate(360deg);
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-20px) rotate(180deg) scale(1.1);
  }
}

/* Earn Badge */
.earn-badge {
  display: inline-block;
  background: rgba(232, 90, 79, 0.1);
  border: 1px solid var(--accent-orange);
  border-radius: 20px;
  padding: 8px 16px;
  color: var(--accent-orange);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 32px;
}

/* Hero Text */
.hero-text {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 64px;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 16px;
}

.title-grow {
  color: var(--accent-orange);
}

.title-wealth {
  color: var(--text-primary);
}

.title-fund {
  color: var(--accent-gold);
}

.title-future {
  color: var(--text-primary);
}

.hero-subtitle {
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 32px;
}

.subtitle-stake {
  color: var(--text-primary);
}

.subtitle-confidence {
  color: var(--accent-orange);
  border: 3px solid var(--accent-orange);
  border-radius: 12px;
  padding: 8px 16px;
  display: inline-block;
}

.hero-description {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 48px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  font-size: 18px;
  padding: 16px 32px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.cta-icon {
  font-size: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .hero {
    padding: 60px 0;
    min-height: 80vh;
  }

  .hero-title {
    font-size: 40px;
  }

  .hero-subtitle {
    font-size: 28px;
  }

  .subtitle-confidence {
    padding: 6px 12px;
    font-size: 24px;
  }

  .hero-description {
    font-size: 16px;
    margin-bottom: 32px;
  }

  .coin {
    width: 80px;
    height: 80px;
    font-size: 32px;
  }

  .coin-2 {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .coin-3 {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .coin-4 {
    width: 80px;
    height: 80px;
    font-size: 32px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 24px;
  }

  .subtitle-confidence {
    font-size: 20px;
    padding: 4px 8px;
  }

  .floating-coins {
    opacity: 0.7;
  }
}
