/* Header - Exact Figma Design */
.header {
  position: absolute;
  top: 24px;
  left: 120px;
  right: 120px;
  height: 72px;
  background: #262626;
  border-radius: 999px;
  z-index: 100;
  box-sizing: border-box;
}

.header-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0 12px 0; /* remove left/right padding for desktop */
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: 38px;
  flex: 0 0 auto;
  margin-right: 40px;
  margin-left: -110px; /* add left margin to shift logo left */
}

.logo-image {
  height: 38px;
  width: auto;
  object-fit: contain;
}

/* Navigation Links */
.nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0px;
  gap: 32px;
  height: 28px;
  flex: 1;
}

.nav-link {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 10px;
  height: 28px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 999px;
  transition: all 0.3s ease;
  flex: none;
  flex-grow: 0;
}

.nav-link:hover {
  color: var(--text-primary);
  background: rgba(64, 64, 64, 0.5);
}

.nav-link.active {
  background: var(--border-color);
  border: 1px solid var(--border-secondary);
  color: var(--text-primary);
  font-weight: 500;
  width: 66px;
}

.header-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 0px;
  gap: 16px;
  height: 48px;
  flex: 0 0 auto;
  margin-right: -110px;
}

.header-actions .btn-secondary {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 79px;
  height: 48px;
  border: 1px solid var(--border-color);
  border-radius: 999px;
  background: transparent;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.header-actions .btn-primary {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 20px;
  gap: 12px;
  min-width: 140px;
  height: 48px;
  background: var(--accent-orange);
  border-radius: 999px;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #FAFAFA;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.header-actions .btn-secondary:hover {
  border-color: var(--accent-orange);
  color: var(--accent-orange);
}

.header-actions .btn-primary:hover {
  background: var(--accent-orange-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(191, 65, 41, 0.3);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

/* Mobile Menu Modal - Exact Figma Design */
.mobile-menu-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 143.89px;
}

.mobile-menu-modal::before {
  content: '';
  position: absolute;
  width: 353px;
  height: 396px;
  left: calc(50% - 353px/2);
  top: 143.89px;
  background: #262626;
  border-radius: 24px;
  box-sizing: border-box;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

/* Close Button */
.mobile-menu-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: transparent;
  border: none;
  color: #F5F5F5;
  font-size: 24px;
  cursor: pointer;
  z-index: 1001;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Nav Links Container - Exact Figma Layout */
.mobile-nav-modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 24px;
  position: absolute;
  width: 131px;
  height: 204px;
  left: calc(50% - 131px/2);
  top: 24px;
  z-index: 1001;
}

/* Individual Nav Links - Exact Figma Styling */
.mobile-nav-link-modal {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 10px;
  height: 28px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 999px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: #D4D4D4;
  text-decoration: none;
  transition: all 0.3s ease;
  flex: none;
  flex-grow: 0;
  white-space: nowrap;
}

/* Active/Home link styling */
.mobile-nav-link-modal.active {
  font-weight: 500;
  color: #F5F5F5;
}

.mobile-nav-link-modal:hover {
  background: #4a4a4a;
  border-color: #626262;
  color: #F5F5F5;
}

/* Buttons Container - Exact Figma Layout */
.mobile-actions-modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0px;
  gap: 24px;
  position: absolute;
  width: 305px;
  height: 120px;
  left: calc(50% - 305px/2);
  top: 252px;
  z-index: 1001;
}

/* Get Started Button - Exact Figma Design */
.mobile-btn-modal {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 305px;
  height: 48px;
  background: #BF4129;
  border-radius: 981.716px;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #FAFAFA;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.mobile-btn-modal:hover {
  background: #A63621;
}

/* Login Button - Exact Figma Design */
.mobile-btn-secondary-modal {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 305px;
  height: 48px;
  border: 1px solid #404040;
  border-radius: 999px;
  background: transparent;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #F5F5F5;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

.mobile-btn-secondary-modal:hover {
  border-color: #525252;
  background: rgba(64, 64, 64, 0.3);
}

/* Hide old mobile menu on mobile */
@media (max-width: 768px) {
  .mobile-menu {
    display: none !important;
  }
}

/* Modal */
.mobile-menu-modal {
  box-sizing: border-box;
  position: absolute;
  width: 353px;
  height: 396px;
  left: 50%;
  top: 143.89px;
  transform: translateX(-50%);
  background: #262626;
  border-radius: 24px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
}

/* Close button */
.mobile-menu-close {
  position: absolute;
  top: 16px;
  right: 24px;
  background: none;
  border: none;
  color: #fff;
  font-size: 32px;
  cursor: pointer;
  z-index: 10001;
  line-height: 1;
}

/* Nav Links */
.mobile-nav-modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;
  gap: 24px;
  position: absolute;
  width: 131px;
  height: 204px;
  left: 50%;
  top: 24px;
  transform: translateX(-50%);
}

/* Nav Link */
.mobile-nav-link-modal {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 10px;
  border-radius: 999px;
  border: 1px solid #525252;
  background: #404040;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: #D4D4D4;
  text-decoration: none;
  transition: background 0.2s, color 0.2s;
  min-width: 50px;
  height: 28px;
}

.mobile-nav-link-modal.active,
.mobile-nav-link-modal:focus,
.mobile-nav-link-modal:hover {
  background: #404040;
  color: #F5F5F5;
  font-weight: 500;
  border: 1px solid #525252;
}

.mobile-nav-link-modal:nth-child(1) { width: 66px; }
.mobile-nav-link-modal:nth-child(2) { width: 92px; }
.mobile-nav-link-modal:nth-child(3) { width: 102px; }
.mobile-nav-link-modal:nth-child(4) { width: 131px; }
.mobile-nav-link-modal:nth-child(5) { width: 89px; }

/* Modal Buttons */
.mobile-actions-modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 24px;
  position: absolute;
  width: 305px;
  height: 120px;
  left: 50%;
  top: 252px;
  transform: translateX(-50%);
}

.mobile-btn-modal {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 305px;
  height: 48px;
  background: #BF4129;
  border-radius: 981.716px;
  color: #FAFAFA;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  border: none;
  cursor: pointer;
}

.mobile-btn-secondary-modal {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 305px;
  height: 48px;
  border: 1px solid #404040;
  border-radius: 999px;
  background: transparent;
  color: #F5F5F5;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
}

/* Responsive: modal full width on small screens */
@media (max-width: 400px) {
  .mobile-menu-modal {
    width: 98vw;
    left: 1vw;
    transform: none;
    min-width: unset;
    max-width: 100vw;
  }
  .mobile-actions-modal,
  .mobile-btn-modal,
  .mobile-btn-secondary-modal {
    width: 90vw;
    left: 5vw;
    min-width: unset;
    max-width: 100vw;
  }
  .mobile-nav-modal {
    width: 90vw;
    left: 5vw;
    min-width: unset;
    max-width: 100vw;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header {
    left: 60px;
    right: 60px;
  }

  .header-content {
    /* Restore horizontal padding for tablet and below if needed */
    padding: 12px 24px;
    gap: 150px;
  }

  .logo {
    margin-left: 0;
  }

  .nav {
    width: 400px;
    gap: 16px;
  }

  .header-actions {
    margin-right: 0; /* reset for tablet and below */
  }
}

@media (max-width: 768px) {
  .header {
    left: 20px;
    right: 20px;
    height: 60px;
  }

  .header-content {
    padding: 8px 16px;
    gap: 20px;
  }

  .logo {
    margin-left: 0;
  }

  .logo-image {
    height: 30px;
  }

  /* Hide desktop navigation and actions */
  .desktop-nav,
  .desktop-actions {
    display: none;
  }

  /* Show mobile menu toggle */
  .mobile-menu-toggle {
    display: flex;
  }

  /* Show mobile menu */
  .mobile-menu {
    display: block;
  }

  .header-actions {
    margin-right: 0; /* ensure no extra margin on mobile */
  }
}

@media (max-width: 480px) {
  .header {
    left: 10px;
    right: 10px;
    top: 12px;
  }

  .logo-image {
    height: 24px;
  }

  .mobile-menu {
    padding: 16px;
  }

  .mobile-nav-link {
    padding: 10px 12px;
    font-size: 14px;
  }
}
