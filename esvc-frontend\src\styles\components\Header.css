/* Header - Exact Figma Design */
.header {
  position: absolute;
  top: 24px;
  left: 120px;
  right: 120px;
  height: 72px;
  background: #262626;
  border-radius: 999px;
  z-index: 100;
  box-sizing: border-box;
}

.header-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 167px;
  height: 38px;
  flex: 0 0 auto;
}

.logo-image {
  height: 38px;
  width: auto;
  object-fit: contain;
}

/* Navigation Links */
.nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0px;
  gap: 32px;
  height: 28px;
  flex: 1;
}

.nav-link {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 10px;
  height: 28px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 999px;
  transition: all 0.3s ease;
  flex: none;
  flex-grow: 0;
}

.nav-link:hover {
  color: var(--text-primary);
  background: rgba(64, 64, 64, 0.5);
}

.nav-link.active {
  background: var(--border-color);
  border: 1px solid var(--border-secondary);
  color: var(--text-primary);
  font-weight: 500;
  width: 66px;
}

/* Header Actions - Buttons */
.header-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 0px;
  gap: 16px;
  height: 48px;
  flex: 0 0 auto;
}

.header-actions .btn-secondary {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 79px;
  height: 48px;
  border: 1px solid var(--border-color);
  border-radius: 999px;
  background: transparent;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.header-actions .btn-primary {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 127px;
  height: 48px;
  background: var(--accent-orange);
  border-radius: 981.716px;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #FAFAFA;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.header-actions .btn-secondary:hover {
  border-color: var(--accent-orange);
  color: var(--accent-orange);
}

.header-actions .btn-primary:hover {
  background: var(--accent-orange-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(191, 65, 41, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header {
    left: 60px;
    right: 60px;
  }

  .header-content {
    gap: 150px;
  }

  .nav {
    width: 400px;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .header {
    left: 20px;
    right: 20px;
    height: 60px;
  }

  .header-content {
    padding: 8px 16px;
    gap: 20px;
  }

  .logo {
    width: 120px;
  }

  .logo-image {
    height: 30px;
  }

  .nav {
    display: none;
  }

  .header-actions {
    gap: 12px;
    width: auto;
  }

  .header-actions .btn-secondary {
    width: 60px;
    padding: 8px 12px;
    font-size: 14px;
  }

  .header-actions .btn-primary {
    width: 100px;
    padding: 8px 12px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .header {
    left: 10px;
    right: 10px;
    top: 12px;
  }

  .logo-image {
    height: 24px;
  }

  .header-actions .btn-secondary {
    display: none;
  }
}
